-- 数据分析接口性能优化索引脚本
-- 为游戏管理员数据分析接口创建必要的数据库索引

-- =============================================
-- 用户活跃度指标相关索引
-- =============================================

-- 游戏历史表索引 - 用于每日游戏启动次数统计
CREATE INDEX IF NOT EXISTS idx_game_histories_created_at 
ON game_histories (created_at);

CREATE INDEX IF NOT EXISTS idx_game_histories_date_user 
ON game_histories (DATE(created_at), user_id);

-- 用户钱包表索引 - 用于APP打开次数和活跃用户统计
CREATE INDEX IF NOT EXISTS idx_user_wallets_last_active_time 
ON user_wallets (last_active_time);

CREATE INDEX IF NOT EXISTS idx_user_wallets_active_time_user 
ON user_wallets (last_active_time, user_id);

-- 用户表索引 - 用于新增玩家统计
CREATE INDEX IF NOT EXISTS idx_users_created_at 
ON users (created_at);

CREATE INDEX IF NOT EXISTS idx_users_date_created 
ON users (DATE(created_at));

-- 会话表索引 - 用于游玩时长统计
CREATE INDEX IF NOT EXISTS idx_sessions_created_updated 
ON sessions (created_at, updated_at);

CREATE INDEX IF NOT EXISTS idx_sessions_date_user 
ON sessions (DATE(created_at), user_id);

-- =============================================
-- 游戏进度指标相关索引
-- =============================================

-- 农场区域表索引 - 用于区域解锁和升级分布统计
CREATE INDEX IF NOT EXISTS idx_farm_plots_area_user 
ON farm_plots (area_id, user_id);

CREATE INDEX IF NOT EXISTS idx_farm_plots_area_level 
ON farm_plots (area_id, level);

CREATE INDEX IF NOT EXISTS idx_farm_plots_level_user 
ON farm_plots (level, user_id);

-- 流水线表索引 - 用于流水线升级分布统计
CREATE INDEX IF NOT EXISTS idx_delivery_lines_level_user 
ON delivery_lines (level, user_id);

CREATE INDEX IF NOT EXISTS idx_delivery_lines_user_level 
ON delivery_lines (user_id, level);

-- =============================================
-- 道具使用指标相关索引
-- =============================================

-- 道具表索引 - 用于道具使用统计
CREATE INDEX IF NOT EXISTS idx_boosters_type_wallet 
ON boosters (type, wallet_id);

CREATE INDEX IF NOT EXISTS idx_boosters_wallet_quantity 
ON boosters (wallet_id, quantity);

-- 激活道具表索引
CREATE INDEX IF NOT EXISTS idx_active_boosters_type_wallet 
ON active_boosters (type, wallet_id);

CREATE INDEX IF NOT EXISTS idx_active_boosters_created_at 
ON active_boosters (created_at);

-- =============================================
-- 任务完成指标相关索引
-- =============================================

-- 任务完成表索引 - 用于任务完成统计
CREATE INDEX IF NOT EXISTS idx_user_task_complete_time 
ON user_task_complete (complete_time);

CREATE INDEX IF NOT EXISTS idx_user_task_complete_date 
ON user_task_complete (DATE(complete_time));

CREATE INDEX IF NOT EXISTS idx_user_task_complete_task_user 
ON user_task_complete (task_id, user_id);

CREATE INDEX IF NOT EXISTS idx_user_task_complete_task_time 
ON user_task_complete (task_id, complete_time);

-- =============================================
-- 社交功能指标相关索引
-- =============================================

-- 用户表邀请相关索引
CREATE INDEX IF NOT EXISTS idx_users_referral_count 
ON users (referral_count);

CREATE INDEX IF NOT EXISTS idx_users_referrer_id 
ON users (referrer_id);

-- =============================================
-- 奖励系统指标相关索引
-- =============================================

-- 宝箱表索引 - 用于宝箱获取统计
CREATE INDEX IF NOT EXISTS idx_chests_created_at 
ON chests (created_at);

CREATE INDEX IF NOT EXISTS idx_chests_date_user 
ON chests (DATE(created_at), user_id);

CREATE INDEX IF NOT EXISTS idx_chests_status_created 
ON chests (status, created_at);

-- 每日签到表索引 - 用于每日宝箱领取统计
CREATE INDEX IF NOT EXISTS idx_user_daily_claims_date 
ON user_daily_claims (claim_date);

CREATE INDEX IF NOT EXISTS idx_user_daily_claims_date_user 
ON user_daily_claims (DATE(claim_date), user_id);

-- 钱包历史表索引 - 用于钻石和GEM获取统计
CREATE INDEX IF NOT EXISTS idx_wallet_histories_currency_action 
ON wallet_histories (currency, action);

CREATE INDEX IF NOT EXISTS idx_wallet_histories_currency_created 
ON wallet_histories (currency, created_at);

CREATE INDEX IF NOT EXISTS idx_wallet_histories_currency_action_created 
ON wallet_histories (currency, action, created_at);

CREATE INDEX IF NOT EXISTS idx_wallet_histories_wallet_currency 
ON wallet_histories (wallet_id, currency);

-- =============================================
-- 付费指标相关索引
-- =============================================

-- IAP购买表索引 - 用于收入统计
CREATE INDEX IF NOT EXISTS idx_iap_purchases_created_status 
ON iap_purchases (created_at, status);

CREATE INDEX IF NOT EXISTS idx_iap_purchases_status_created 
ON iap_purchases (status, created_at);

CREATE INDEX IF NOT EXISTS idx_iap_purchases_wallet_created 
ON iap_purchases (wallet_id, created_at);

-- PHRS充值表索引
CREATE INDEX IF NOT EXISTS idx_phrs_deposits_created_status 
ON phrs_deposits (created_at, status);

CREATE INDEX IF NOT EXISTS idx_phrs_deposits_status_created 
ON phrs_deposits (status, created_at);

CREATE INDEX IF NOT EXISTS idx_phrs_deposits_wallet_created 
ON phrs_deposits (wallet_id, created_at);

-- =============================================
-- 留存率指标相关索引
-- =============================================

-- 用户表和钱包表联合查询索引
CREATE INDEX IF NOT EXISTS idx_users_created_id 
ON users (created_at, id);

-- 复合索引用于留存率计算
CREATE INDEX IF NOT EXISTS idx_user_wallets_user_active 
ON user_wallets (user_id, last_active_time);

-- =============================================
-- 通用性能优化索引
-- =============================================

-- 时间范围查询优化
CREATE INDEX IF NOT EXISTS idx_game_histories_created_desc 
ON game_histories (created_at DESC);

CREATE INDEX IF NOT EXISTS idx_users_created_desc 
ON users (created_at DESC);

CREATE INDEX IF NOT EXISTS idx_wallet_histories_created_desc 
ON wallet_histories (created_at DESC);

-- 用户ID相关查询优化
CREATE INDEX IF NOT EXISTS idx_game_histories_user_created 
ON game_histories (user_id, created_at);

CREATE INDEX IF NOT EXISTS idx_wallet_histories_user_created 
ON wallet_histories (user_id, created_at);

-- =============================================
-- 分析查询专用视图（可选）
-- =============================================

-- 每日游戏启动统计视图
CREATE OR REPLACE VIEW daily_game_starts_view AS
SELECT 
    DATE(created_at) as date,
    COUNT(*) as game_starts,
    COUNT(DISTINCT user_id) as unique_players
FROM game_histories 
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- 每日新增用户统计视图
CREATE OR REPLACE VIEW daily_new_users_view AS
SELECT 
    DATE(created_at) as date,
    COUNT(*) as new_users
FROM users 
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- 区域解锁统计视图
CREATE OR REPLACE VIEW area_unlock_stats_view AS
SELECT 
    area_id,
    COUNT(DISTINCT user_id) as unlocked_players,
    MAX(level) as max_level,
    AVG(level) as avg_level
FROM farm_plots 
WHERE area_id BETWEEN 2 AND 20
GROUP BY area_id
ORDER BY area_id;

-- =============================================
-- 索引使用情况监控查询
-- =============================================

-- 查看索引使用情况的SQL（MySQL）
-- SELECT 
--     TABLE_SCHEMA,
--     TABLE_NAME,
--     INDEX_NAME,
--     CARDINALITY,
--     SUB_PART,
--     PACKED,
--     NULLABLE,
--     INDEX_TYPE
-- FROM INFORMATION_SCHEMA.STATISTICS 
-- WHERE TABLE_SCHEMA = 'your_database_name'
--   AND TABLE_NAME IN ('game_histories', 'users', 'user_wallets', 'farm_plots', 'delivery_lines')
-- ORDER BY TABLE_NAME, INDEX_NAME;

-- 查看慢查询日志设置
-- SHOW VARIABLES LIKE 'slow_query_log%';
-- SHOW VARIABLES LIKE 'long_query_time';

-- =============================================
-- 性能监控建议
-- =============================================

-- 1. 定期分析表统计信息
-- ANALYZE TABLE game_histories, users, user_wallets, farm_plots, delivery_lines;

-- 2. 监控索引使用情况
-- 使用 EXPLAIN 分析查询执行计划

-- 3. 定期清理过期数据
-- 考虑对历史数据进行分区或归档

-- 4. 监控缓存命中率
-- 确保Redis缓存正常工作，减少数据库查询压力

-- =============================================
-- 注意事项
-- =============================================

-- 1. 索引会占用额外存储空间，影响写入性能
-- 2. 根据实际查询模式调整索引策略
-- 3. 定期监控索引使用情况，删除未使用的索引
-- 4. 考虑使用分区表处理大量历史数据
-- 5. 在生产环境中逐步创建索引，避免锁表
