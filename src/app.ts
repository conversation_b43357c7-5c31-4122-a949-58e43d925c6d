// src/app.ts
import express from "express";
import bodyParser from "body-parser";
import './config/env'; // 导入统一的环境配置管理
import { logger } from './utils/logger';

// 增加进程监听器限制，避免MaxListenersExceededWarning
// 因为有多个Worker和服务都需要监听SIGTERM信号
process.setMaxListeners(20);

// 设置时区环境变量
process.env.TZ = process.env.TZ || 'Asia/Shanghai';
import { connectDB, sequelize } from "./config/db";
import './models/index';
import { redis } from "./config/redis";
import { appConfig } from "./config/AppConfig";
import cors from "cors";
import { Server } from 'http';
import { globalErrorHandler, notFoundHandler } from './middlewares/globalErrorHandler';

// 路由导入
import userRoutes from "./routes/userRoutes";
import chestRoutes from "./routes/chestRoutes";
import referralRoutes from "./routes/referralRoutes";
import dailyClaimRoutes from "./routes/dailyClaimRoutes";
import taskRoutes from "./routes/taskRoutes";
import ticketRoutes from "./routes/ticketRoutes";
import freeTicketTransferRoutes from "./routes/freeTicketTransferRoutes";
import walletRoutes from "./routes/walletRoutes";
import web3AuthRoutes from "./routes/web3AuthRoutes";
import gameRouter from "./routes/game";
import reservationRouter from "./routes/reservation";
import resetRouter from "./routes/reset";
import my_reservationsRouter from "./routes/my_reservations";
import rewardsRouter from "./routes/rewards";
import { router as tonProofRouter } from "./routes/tonProof";
import bullKingRoutes from "./routes/bullKingRoutes";
import kolProgressRoutes from "./routes/kolProgressRoutes";
import kolRoutes from "./routes/kolRoutes";
import web3SignTestRoutes from "./routes/web3SignTestRoutes";
import rebateRoutes from "./routes/rebateRoutes";
import bullUnlockRoutes from "./routes/bullUnlockRoutes";
import withdrawalRoutes from "./routes/withdrawalRoutes";
import jackpotChestRoutes from "./routes/jackpotChestRoutes";
import telegramShareRoutes from "./routes/telegramShareRoutes";
import telegramPaymentRoutes from "./routes/telegramPaymentRoutes";
import fragmentRoutes from "./routes/fragmentRoutes";
import testChestRoutes from "./routes/testChestRoutes";
import gemLeaderboardRoutes from "./routes/gemLeaderboardRoutes";
import farmPlotRoutes from "./routes/farmPlotRoutes";
import deliveryLineRoutes from "./routes/deliveryLineRoutes";
import deliveryLineController from "./controllers/deliveryLineController";
import gameLoopRoutes from "./routes/gameLoopRoutes";
import dappPortalPaymentRoutes from "./routes/dappPortalPaymentRoutes";

import iapRoutes from './routes/iapRoutes';
import testResetRoutes from './routes/testResetRoutes';
import healthRoutes from './routes/healthRoutes';
import excelUploadRoutes from './routes/excelUploadRoutes';
import farmConfigRoutes from './routes/farmConfigRoutes';
import newTaskRoutes from './routes/newTaskRoutes';
import adminTaskRoutes from './routes/adminTaskRoutes';
import phrsDepositRoutes from './routes/phrsDepositRoutes';
import phrsPaymentRoutes from './routes/phrsPaymentRoutes';
import phrsPriceRoutes from './routes/admin/phrsPriceRoutes';

// 服务和工具导入
import { scheduleDailyRoundJobs } from "./jobs/scheduleDailyRoundJobs";
import { scheduleKaiaPriceUpdateJob } from "./jobs/scheduleKaiaPriceUpdateJob";
import { schedulePhrsPriceUpdateJob } from "./jobs/schedulePhrsPriceUpdateJob";
import TonCenterClient from "./services/tonService";
import { scheduleDailySessions } from "./scheduler/dailySessions";
import { generateRandomNumber } from "./utils/random";
import { serviceManager } from "./services/ServiceManager";
import { FarmConfigService } from "./services/farmConfigService";
import { backgroundTaskController } from "./services/BackgroundTaskController";

// 应用语言中间件
import { languageMiddleware } from "./middlewares/languageMiddleware";

// 初始化 TonCenter 客户端并获取区块信息
const initTonClient = async () => {
  const apiKey = process.env.TONCENTER_API_KEY;
  if (!apiKey) {
    logger.warn("TONCENTER_API_KEY not found in environment variables");
    return;
  }
  
  const tonClient = new TonCenterClient(apiKey);
  
  try {
    const blockNumber = await tonClient.getLatestBlockNumber();
    const winerNum = generateRandomNumber(blockNumber);

    logger.info("TonCenter block info", { winerNum, blockNumber });
  } catch (error) {
    logger.error("Failed to get block information", { error: error instanceof Error ? error.message : error });
  }
};

async function main() {
  // 验证配置
  const configErrors = appConfig.validate();
  if (configErrors.length > 0) {
    logger.error('配置验证失败');
    configErrors.forEach(error => logger.error(`- ${error}`));
    process.exit(1);
  }
  logger.info(`Wolf.Fun 服务器启动 [${appConfig.server.environment}]`);

  // 初始化 TonCenter 客户端
  await initTonClient();

  const app = express();
  let serverInstance: Server | null = null;

  // 代理设置 - 信任代理服务器（如 Nginx、Cloudflare、AWS ALB 等）
  // 这对于正确获取客户端真实 IP 地址和 express-rate-limit 正常工作是必需的
  if (process.env.TRUST_PROXY === 'true' || appConfig.server.environment === 'production') {
    // 在生产环境或明确配置时信任代理
    app.set('trust proxy', true);
    logger.info('已启用 trust proxy 设置');
  } else if (process.env.TRUST_PROXY_HOPS) {
    // 如果指定了代理跳数，使用具体数值
    const hops = parseInt(process.env.TRUST_PROXY_HOPS, 10);
    app.set('trust proxy', hops);
    logger.info(`已设置 trust proxy 跳数: ${hops}`);
  }

  // 中间件设置
  app.disable('x-powered-by'); // 禁用 X-Powered-By 头信息
  app.use(cors());
  app.use(bodyParser.json());
  app.use(bodyParser.urlencoded({ extended: true }));
  app.use(languageMiddleware);

  // 路由设置
  app.use("/api/user", userRoutes);
  app.use("/api/chest", chestRoutes);
  app.use("/api/referral", referralRoutes);
  app.use("/api/invite", dailyClaimRoutes);
  app.use("/api/ton-proof", tonProofRouter);
  app.use("/api/tasks", taskRoutes);
  app.use("/api/ticket", ticketRoutes);
  app.use("/api/wallet", walletRoutes);
  app.use("/api/game", gameRouter);
  app.use("/api/game", reservationRouter);
  app.use("/api/game", my_reservationsRouter);
  app.use("/api/reset", resetRouter);
  app.use("/api/rewards", rewardsRouter);
  app.use("/api/bull-king", bullKingRoutes);
  app.use("/api/kol-progress", kolProgressRoutes);
  app.use("/api/kol", kolRoutes);
  app.use("/api/rebate", rebateRoutes);
  app.use("/api/bull-unlock", bullUnlockRoutes);
  app.use("/api/withdrawal", withdrawalRoutes);
  app.use("/api/jackpot-chest", jackpotChestRoutes);
  app.use("/api/telegram-share", telegramShareRoutes);
  app.use("/api/telegram-payment", telegramPaymentRoutes);
  app.use("/api/fragment", fragmentRoutes);
  app.use("/api/free-ticket", freeTicketTransferRoutes);
  app.use("/api/test-chest", testChestRoutes);
  app.use("/api/gem-leaderboard", gemLeaderboardRoutes);
  app.use("/api/web3-auth", web3AuthRoutes);
  app.use("/api/farm", farmPlotRoutes);
  app.use("/api/delivery", deliveryLineRoutes);
  app.use("/api/game-loop", gameLoopRoutes);
  app.use("/api/web3-sign-test", web3SignTestRoutes);
  app.use("/api/dapp-portal-payment", dappPortalPaymentRoutes);
  app.use('/api/iap', iapRoutes);
  app.use('/api/health', healthRoutes);
  app.use('/api/excel', excelUploadRoutes);
  app.use('/api/admin/farm-config', farmConfigRoutes);
  // 新任务系统路由
  app.use('/api/new-tasks', newTaskRoutes);
  app.use('/api/admin/tasks', adminTaskRoutes);
  app.use('/api/phrs-deposit', phrsDepositRoutes);
  app.use('/api/phrs-payment', phrsPaymentRoutes);
  app.use('/api/admin/phrs-price', phrsPriceRoutes);
  // 测试重置路由（仅在开发环境下可用）
  app.use('/api/test', testResetRoutes);

  // 测试流水线配置路由（无需认证）
  app.get('/api/test/delivery-line-configs', (req, res) => {
    deliveryLineController.testGetConfigs(req as any, res);
  });

  // 404 处理中间件（必须在所有路由之后）
  app.use(notFoundHandler);

  // 全局错误处理中间件（必须在最后）
  app.use(globalErrorHandler);

  // 数据库连接
  try {
    await connectDB();
    // await sequelize.sync(); // 根据模型自动建表(开发模式可用,生产请谨慎)
    logger.info('数据库连接成功');
  } catch (error) {
    logger.error('数据库连接失败', { error: error instanceof Error ? error.message : error });
    process.exit(1);
  }

  // Redis 测试
  try {
    await redis.set("wolffun_test", "hello");
    const val = await redis.get("wolffun_test");
    logger.debug("Redis test value", { value: val });
  } catch (error) {
    logger.error('Redis 测试失败', { error: error instanceof Error ? error.message : error });
  }

  // 启动 HTTP 服务器
  serverInstance = app.listen(appConfig.server.port, () => {
    logger.info(`Wolf.Fun game server running on port ${appConfig.server.port}...`);
  });
  
  // 设置服务器实例到服务管理器
  serviceManager.setServerInstance(serverInstance);
  
  serverInstance.on('error', (error) => {
    logger.error('HTTP 服务器错误', { error: error instanceof Error ? error.message : error });
    serviceManager.gracefulShutdown('服务器错误');
  });

  // 启动每日 Session 预生成任务
  if (backgroundTaskController.shouldRunScheduledJobs()) {
    try {
      scheduleDailySessions();
      logger.info("✅ 每日 Session 预生成任务已启动");
    } catch (error) {
      logger.error('启动每日 Session 预生成任务失败', { error: error instanceof Error ? error.message : error });
    }
  } else {
    logger.info("⏸️  每日 Session 预生成任务已被环境变量禁用");
  }

  // 调度当天所有场次的抽奖任务
  if (backgroundTaskController.shouldRunLotteryJobs()) {
    try {
      await scheduleDailyRoundJobs();
      logger.info("✅ 所有抽奖任务已调度");
    } catch (err) {
      logger.error("调度抽奖任务失败", { error: err instanceof Error ? err.message : err });
    }
  } else {
    logger.info("⏸️  抽奖任务调度已被环境变量禁用");
  }

  // 调度Kaia价格更新任务
  if (backgroundTaskController.shouldRunPriceUpdateJobs()) {
    try {
      await scheduleKaiaPriceUpdateJob();
      logger.info("✅ Kaia价格更新任务已调度");
    } catch (err) {
      logger.error("调度Kaia价格更新任务失败", { error: err instanceof Error ? err.message : err });
    }
  } else {
    logger.info("⏸️  Kaia价格更新任务已被环境变量禁用");
  }

  // 调度PHRS价格更新任务
  if (backgroundTaskController.shouldRunPriceUpdateJobs()) {
    try {
      await schedulePhrsPriceUpdateJob();
      logger.info("✅ PHRS价格更新任务已调度");
    } catch (err) {
      logger.error("调度PHRS价格更新任务失败", { error: err instanceof Error ? err.message : err });
    }
  } else {
    logger.info("⏸️  PHRS价格更新任务已被环境变量禁用");
  }

  // 初始化服务管理器
  try {
    await serviceManager.initializeQueues();
    serviceManager.setupCronJobs();
    await serviceManager.initializeJackpotTasks();
    logger.info('服务管理器初始化完成');
  } catch (err) {
    logger.error('初始化服务管理器失败', { error: err instanceof Error ? err.message : err });
  }

  // 预热农场配置缓存
  try {
    await FarmConfigService.warmupCache();
    logger.info('✅ 农场配置缓存预热完成');
  } catch (error) {
    logger.warn('⚠️ 农场配置缓存预热失败', { error: error instanceof Error ? error.message : error });
  }

  const serverInfo = {
    api: appConfig.server.apiName,
    port: appConfig.server.port,
    instance: appConfig.server.apiInstance,
    environment: process.env.NODE_ENV || 'development',
    database: `${process.env.DB_NAME} (${process.env.DB_HOST}:${process.env.DB_PORT})`,
    redis: `${process.env.REDIS_HOST}:${process.env.REDIS_PORT}`,
    urls: {
      api: `http://localhost:${appConfig.server.port}/api`,
      docs: `http://localhost:${appConfig.server.port}/api-docs`,
      health: `http://localhost:${appConfig.server.port}/api/health`,
      admin: `http://localhost:${appConfig.server.port}/api/admin/farm-config`
    }
  };
  logger.info('='.repeat(60));
  logger.info(`🚀 ${serverInfo.api} is running on port ${serverInfo.port}`);
  logger.info(`🏷️  API Instance: ${serverInfo.instance}`);
  logger.info(`📊 Environment: ${serverInfo.environment}`);
  logger.info(`🗜️  Database: ${serverInfo.database}`);
  logger.info(`⚡ Redis: ${serverInfo.redis}`);
  logger.info('Server URLs', serverInfo.urls);
  logger.info('='.repeat(60));
  // 启动PHRS充值监控服务
  try {
    const { phrsDepositMonitor } = await import('./jobs/phrsDepositMonitor');
    phrsDepositMonitor.start();
    logger.info('PHRS充值监控服务已启动');
  } catch (err) {
    logger.error('启动PHRS充值监控服务失败', { error: err instanceof Error ? err.message : err });
  }

  // 启动PHRS余额监控服务
  try {
    const { phrsBalanceMonitor } = await import('./services/phrsBalanceMonitor');
    phrsBalanceMonitor.start();
    logger.info('PHRS余额监控服务已启动');
  } catch (err) {
    logger.error('启动PHRS余额监控服务失败', { error: err instanceof Error ? err.message : err });
  }

  // 设置进程事件监听
  process.on('SIGTERM', () => serviceManager.gracefulShutdown('SIGTERM 信号'));
  process.on('SIGINT', () => serviceManager.gracefulShutdown('SIGINT 信号'));
  process.on('uncaughtException', async (error) => {
    logger.error('未捕获的异常', { error: error instanceof Error ? error.message : error });
    await serviceManager.gracefulShutdown('未捕获的异常');
  });
  process.on('unhandledRejection', async (reason, promise) => {
    logger.error('未处理的 Promise 拒绝', { reason, promise });
    await serviceManager.gracefulShutdown('未处理的 Promise 拒绝');
  });
}

main().catch(async (err) => {
    logger.error("服务器启动失败", { error: err instanceof Error ? err.message : err });
    await serviceManager.gracefulShutdown('启动失败');
});